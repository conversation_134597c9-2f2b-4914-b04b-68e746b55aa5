[{"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.posts\\` has a row level security policy \\`Users can insert their own posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_posts_Users can insert their own posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.public_keys_backup\\` has a row level security policy \\`Users can insert their own backup keys\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "public_keys_backup", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_public_keys_backup_Users can insert their own backup keys"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.posts_optimized\\` has a row level security policy \\`Authenticated users can view optimized posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "posts_optimized", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_posts_optimized_Authenticated users can view optimized posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.posts_optimized\\` has a row level security policy \\`System can delete optimized posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "posts_optimized", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_posts_optimized_System can delete optimized posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.posts_optimized\\` has a row level security policy \\`System can insert optimized posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "posts_optimized", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_posts_optimized_System can insert optimized posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.posts_optimized\\` has a row level security policy \\`System can update optimized posts\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "posts_optimized", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_posts_optimized_System can update optimized posts"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.room_participants\\` has a row level security policy \\`join_room\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_room_participants_join_room"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.room_messages\\` has a row level security policy \\`room_messages_insert_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "room_messages", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_room_messages_room_messages_insert_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.notifications\\` has a row level security policy \\`notifications_insert_policy\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "notifications", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_notifications_notifications_insert_policy"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.maintenance_log\\` has a row level security policy \\`Only system can delete maintenance logs\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "maintenance_log", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_maintenance_log_Only system can delete maintenance logs"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.maintenance_log\\` has a row level security policy \\`Only system can insert maintenance logs\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "maintenance_log", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_maintenance_log_Only system can insert maintenance logs"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.maintenance_log\\` has a row level security policy \\`Only system can update maintenance logs\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "maintenance_log", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_maintenance_log_Only system can update maintenance logs"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.maintenance_log\\` has a row level security policy \\`Only system can view maintenance logs\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "maintenance_log", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_maintenance_log_Only system can view maintenance logs"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.message_reactions\\` has multiple permissive policies for role \\`anon\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their own reactions\",\"Users can view reactions on accessible messages\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "message_reactions", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_message_reactions_anon_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.message_reactions\\` has multiple permissive policies for role \\`authenticated\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their own reactions\",\"Users can view reactions on accessible messages\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "message_reactions", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_message_reactions_authenticated_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.message_reactions\\` has multiple permissive policies for role \\`authenticator\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their own reactions\",\"Users can view reactions on accessible messages\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "message_reactions", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_message_reactions_authenticator_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.message_reactions\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their own reactions\",\"Users can view reactions on accessible messages\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "message_reactions", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_message_reactions_dashboard_user_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`anon\\` for action \\`DELETE\\`. Policies include \\`{\"Users can delete their own posts\",posts_delete_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_anon_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`anon\\` for action \\`INSERT\\`. Policies include \\`{\"Users can insert their own posts\",posts_insert_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_anon_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`anon\\` for action \\`SELECT\\`. Policies include \\`{\"Tagged users can view posts they're tagged in\",\"Users can view all posts\",posts_select_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_anon_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`anon\\` for action \\`UPDATE\\`. Policies include \\`{\"Users can update their own posts\",posts_update_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_anon_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`authenticated\\` for action \\`DELETE\\`. Policies include \\`{\"Users can delete their own posts\",posts_delete_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_authenticated_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`authenticated\\` for action \\`INSERT\\`. Policies include \\`{\"Users can insert their own posts\",posts_insert_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_authenticated_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`authenticated\\` for action \\`SELECT\\`. Policies include \\`{\"Tagged users can view posts they're tagged in\",\"Users can view all posts\",posts_select_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_authenticated_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`authenticated\\` for action \\`UPDATE\\`. Policies include \\`{\"Users can update their own posts\",posts_update_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_authenticated_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`authenticator\\` for action \\`DELETE\\`. Policies include \\`{\"Users can delete their own posts\",posts_delete_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_authenticator_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`authenticator\\` for action \\`INSERT\\`. Policies include \\`{\"Users can insert their own posts\",posts_insert_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_authenticator_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`authenticator\\` for action \\`SELECT\\`. Policies include \\`{\"Tagged users can view posts they're tagged in\",\"Users can view all posts\",posts_select_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_authenticator_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`authenticator\\` for action \\`UPDATE\\`. Policies include \\`{\"Users can update their own posts\",posts_update_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_authenticator_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`DELETE\\`. Policies include \\`{\"Users can delete their own posts\",posts_delete_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_dashboard_user_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`INSERT\\`. Policies include \\`{\"Users can insert their own posts\",posts_insert_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_dashboard_user_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`SELECT\\`. Policies include \\`{\"Tagged users can view posts they're tagged in\",\"Users can view all posts\",posts_select_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_dashboard_user_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.posts\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`UPDATE\\`. Policies include \\`{\"Users can update their own posts\",posts_update_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "posts", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_posts_dashboard_user_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`anon\\` for action \\`DELETE\\`. Policies include \\`{delete_room_participants,leave_room}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_anon_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`anon\\` for action \\`INSERT\\`. Policies include \\`{insert_room_participants,join_room}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_anon_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`authenticated\\` for action \\`DELETE\\`. Policies include \\`{delete_room_participants,leave_room}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_authenticated_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`authenticated\\` for action \\`INSERT\\`. Policies include \\`{insert_room_participants,join_room}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_authenticated_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`authenticator\\` for action \\`DELETE\\`. Policies include \\`{delete_room_participants,leave_room}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_authenticator_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`authenticator\\` for action \\`INSERT\\`. Policies include \\`{insert_room_participants,join_room}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_authenticator_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`DELETE\\`. Policies include \\`{delete_room_participants,leave_room}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_dashboard_user_DELETE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.room_participants\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`INSERT\\`. Policies include \\`{insert_room_participants,join_room}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "room_participants", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_room_participants_dashboard_user_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`anon\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their typing status\",\"Users can view chat typing status\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_anon_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`authenticated\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their typing status\",\"Users can view chat typing status\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_authenticated_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`authenticator\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their typing status\",\"Users can view chat typing status\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_authenticator_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.typing_status\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`SELECT\\`. Policies include \\`{\"Users can manage their typing status\",\"Users can view chat typing status\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "typing_status", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_typing_status_dashboard_user_SELECT"}, {"name": "duplicate_index", "title": "Duplicate Index", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects cases where two ore more identical indexes exist.", "detail": "Table \\`public.posts\\` has identical indexes {idx_posts_created_at,idx_posts_created_at_desc,idx_posts_created_desc}. Drop all except one of them", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0009_duplicate_index", "metadata": {"name": "posts", "type": "table", "schema": "public", "indexes": ["idx_posts_created_at", "idx_posts_created_at_desc", "idx_posts_created_desc"]}, "cache_key": "duplicate_index_public_posts_{idx_posts_created_at,idx_posts_created_at_desc,idx_posts_created_desc}"}, {"name": "duplicate_index", "title": "Duplicate Index", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects cases where two ore more identical indexes exist.", "detail": "Table \\`public.posts\\` has identical indexes {idx_posts_user_created,idx_posts_user_id_created_at}. Drop all except one of them", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0009_duplicate_index", "metadata": {"name": "posts", "type": "table", "schema": "public", "indexes": ["idx_posts_user_created", "idx_posts_user_id_created_at"]}, "cache_key": "duplicate_index_public_posts_{idx_posts_user_created,idx_posts_user_id_created_at}"}, {"name": "duplicate_index", "title": "Duplicate Index", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects cases where two ore more identical indexes exist.", "detail": "Table \\`public.reel_comments\\` has identical indexes {idx_reel_comments_user_id,reel_comments_user_id_idx}. Drop all except one of them", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0009_duplicate_index", "metadata": {"name": "reel_comments", "type": "table", "schema": "public", "indexes": ["idx_reel_comments_user_id", "reel_comments_user_id_idx"]}, "cache_key": "duplicate_index_public_reel_comments_{idx_reel_comments_user_id,reel_comments_user_id_idx}"}]