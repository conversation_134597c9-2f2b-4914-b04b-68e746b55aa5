{"name": "klicktape", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "setup-eas-env": "node ./scripts/setup-eas-env.sh", "setup-eas-env:windows": "powershell -ExecutionPolicy Bypass -File ./scripts/setup-eas-env.ps1", "validate-security": "node ./scripts/validate-security.js", "env:pull": "eas env:pull --environment development", "env:list": "eas env:list", "build:dev": "eas build --platform ios --profile development", "build:preview": "eas build --platform all --profile preview", "build:prod": "eas build --platform all --profile production"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@craftzdog/react-native-buffer": "^6.0.5", "@expo/ui": "~0.1.1-alpha.10", "@expo/vector-icons": "^14.0.2", "@google/genai": "^1.10.0", "@noble/curves": "^1.0.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "11.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.4.4", "@reduxjs/toolkit": "^2.7.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/node-forge": "^1.3.11", "@upstash/redis": "^1.35.1", "base-64": "^1.0.0", "buffer": "^6.0.3", "crypto-es": "^2.1.0", "crypto-js": "^4.2.0", "expo": "^53.0.20", "expo-av": "~15.1.7", "expo-blur": "~14.1.5", "expo-camera": "~16.1.11", "expo-constants": "~17.1.5", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-file-system": "^18.1.11", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-network": "~7.1.5", "expo-router": "~5.1.4", "expo-screen-orientation": "~8.1.7", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-video": "~2.2.2", "expo-video-thumbnails": "~9.1.3", "expo-web-browser": "~14.2.0", "firebase": "^12.0.0", "jsrsasign": "^11.1.0", "lodash.debounce": "^4.0.8", "moment": "^2.30.1", "nativewind": "^4.1.23", "node-forge": "^1.3.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-crypto-js": "^1.0.0", "react-native-device-info": "^14.0.4", "react-native-encrypted-storage": "^4.0.3", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-keychain": "^10.0.0", "react-native-modal": "^14.0.0-rc.1", "react-native-polyfill-globals": "^3.1.0", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-rsa-native": "^2.0.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-ssl-pinning": "^1.6.0", "react-native-swiper": "^1.6.0", "react-native-toast-message": "^2.3.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/base-64": "^1.0.2", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.12", "@types/jsrsasign": "^10.5.15", "@types/lodash": "^4.17.16", "@types/react": "~19.0.10", "@types/react-test-renderer": "^19.1.0", "jest": "^29.2.1", "jest-expo": "~53.0.9", "supabase": "^2.22.6", "typescript": "^5.3.3"}, "private": true}